#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搞笑按键语音玩具启动器
支持虚拟环境和全局安装两种方式
"""

import subprocess
import sys
import os

def create_virtual_env():
    """创建虚拟环境"""
    print("🏗️  正在创建虚拟环境...")
    try:
        subprocess.check_call([sys.executable, "-m", "venv", "venv"])
        print("✅ 虚拟环境创建完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 虚拟环境创建失败: {e}")
        return False

def install_requirements_in_venv():
    """在虚拟环境中安装依赖包"""
    print("🔧 正在虚拟环境中安装依赖包...")
    try:
        if os.name == 'nt':  # Windows
            pip_path = os.path.join("venv", "Scripts", "pip")
        else:  # macOS/Linux
            pip_path = os.path.join("venv", "bin", "pip")

        subprocess.check_call([pip_path, "install", "-r", "requirements.txt"])
        print("✅ 虚拟环境依赖包安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 虚拟环境依赖包安装失败: {e}")
        return False

def install_requirements_global():
    """全局安装依赖包"""
    print("🔧 正在全局安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 全局依赖包安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 全局依赖包安装失败: {e}")
        return False

def run_with_venv():
    """使用虚拟环境运行程序"""
    try:
        if os.name == 'nt':  # Windows
            python_path = os.path.join("venv", "Scripts", "python")
        else:  # macOS/Linux
            python_path = os.path.join("venv", "bin", "python")

        # 优先启动工作版，如果失败则启动命令行版
        try:
            subprocess.run([python_path, "main_working.py"])
        except:
            print("🔄 GUI版本启动失败，切换到命令行版本...")
            subprocess.run([python_path, "main_cli.py"])
        return True
    except Exception as e:
        print(f"❌ 虚拟环境运行失败: {e}")
        return False

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import pynput
        import pyttsx3
        import tkinter
        return True
    except ImportError:
        return False

def main():
    print("🎮 欢迎使用搞笑按键语音玩具！")
    print("=" * 50)

    # 检查是否存在虚拟环境
    venv_exists = os.path.exists("venv")

    if venv_exists:
        print("📁 发现虚拟环境目录")
        print("🚀 使用虚拟环境启动程序...")
        if run_with_venv():
            return
        else:
            print("⚠️  虚拟环境启动失败，尝试全局方式...")

    # 检查全局依赖
    if check_dependencies():
        print("✅ 全局依赖已安装")
        print("🚀 启动程序...")
        try:
            # 优先启动工作版
            subprocess.run([sys.executable, "main_working.py"])
            return
        except:
            print("🔄 GUI版本启动失败，切换到命令行版本...")
            subprocess.run([sys.executable, "main_cli.py"])
            return
        except Exception as e:
            print(f"❌ 程序启动失败: {e}")

    # 依赖缺失，询问安装方式
    print("⚠️  缺少依赖包")
    print("\n选择安装方式：")
    print("1. 创建虚拟环境并安装（推荐，项目独立）")
    print("2. 全局安装（简单，但可能影响其他项目）")
    print("3. 取消")

    choice = input("\n请选择 (1/2/3): ").strip()

    if choice == "1":
        # 虚拟环境方式
        if not venv_exists:
            if not create_virtual_env():
                print("❌ 虚拟环境创建失败")
                return

        if install_requirements_in_venv():
            print("🚀 启动程序...")
            run_with_venv()
        else:
            print("❌ 安装失败")

    elif choice == "2":
        # 全局安装方式
        if install_requirements_global():
            print("🚀 启动程序...")
            try:
                from main import FunnyKeyboardToy
                app = FunnyKeyboardToy()
                app.run()
            except Exception as e:
                print(f"❌ 程序启动失败: {e}")
        else:
            print("❌ 安装失败")

    else:
        print("👋 已取消")
        return

    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
