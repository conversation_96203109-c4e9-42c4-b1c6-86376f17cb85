#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搞笑按键语音玩具 - 工作版本
使用轮询方式避免tkinter和pynput的冲突
"""

import tkinter as tk
import subprocess
import threading
import time
import sys
import os
import tempfile
import random

class FunnyKeyboardToy:
    def __init__(self):
        # 搞笑语音内容映射
        self.funny_sounds = {
            '1': "回答我！为什么不说话？",
            '2': "看着我的眼睛！不要逃避！",
            '3': "告诉我为什么？我很好奇！",
            '4': "你在干什么？快说实话！",
            '5': "不可能！我不相信！",
            '6': "哈哈哈哈！太搞笑了！",
            '7': "我不信！你在骗我！",
            '8': "真的吗？确定不是开玩笑？",
            '9': "太离谱了！怎么可能！",
            '0': "好吧好吧，我投降了！",
            'q': "什么鬼？这是什么操作？",
            'w': "我的天哪！吓死我了！",
            'e': "额...这个...怎么说呢...",
            'r': "让我想想...嗯...不对！",
            't': "太好玩了！再来一次！",
            'y': "耶！成功了！",
            'u': "呃...你确定吗？",
            'i': "我觉得不对劲...",
            'o': "哦！原来如此！",
            'p': "噗！忍不住笑了！"
        }
        
        self.key_count = 0
        self.running = True
        self.last_keys = set()
        
        # 创建GUI
        self.setup_gui()
        
        # 启动键盘监听（使用外部进程）
        self.start_keyboard_monitor()
        
        # 启动按键检查
        self.check_keys()
        
    def setup_gui(self):
        """创建图形界面"""
        self.root = tk.Tk()
        self.root.title("🎮 搞笑按键语音玩具 - 工作版")
        self.root.geometry("500x400")
        self.root.configure(bg='#2c3e50')
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.quit_app)
        
        # 标题
        title_label = tk.Label(
            self.root, 
            text="🎮 搞笑按键语音玩具", 
            font=("Arial", 18, "bold"),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # 当前按键显示
        self.current_key_var = tk.StringVar(value="等待按键...")
        current_key_label = tk.Label(
            self.root,
            textvariable=self.current_key_var,
            font=("Arial", 14, "bold"),
            fg='#e74c3c',
            bg='#34495e',
            relief='raised',
            bd=2,
            padx=15,
            pady=8
        )
        current_key_label.pack(pady=15)
        
        # 语音内容显示
        self.speech_var = tk.StringVar(value="按下按键开始游戏！")
        speech_label = tk.Label(
            self.root,
            textvariable=self.speech_var,
            font=("Arial", 12),
            fg='#f39c12',
            bg='#2c3e50',
            wraplength=400,
            justify='center'
        )
        speech_label.pack(pady=10)
        
        # 按键说明
        instruction_label = tk.Label(
            self.root,
            text="🎯 支持的按键：数字键 0-9, 字母键 Q-P",
            font=("Arial", 10),
            fg='#95a5a6',
            bg='#2c3e50'
        )
        instruction_label.pack(pady=10)
        
        # 统计信息
        self.stats_var = tk.StringVar(value="按键次数: 0")
        stats_label = tk.Label(
            self.root,
            textvariable=self.stats_var,
            font=("Arial", 10),
            fg='#7f8c8d',
            bg='#2c3e50'
        )
        stats_label.pack(pady=5)
        
        # 状态显示
        self.status_var = tk.StringVar(value="🔄 正在启动键盘监听...")
        status_label = tk.Label(
            self.root,
            textvariable=self.status_var,
            font=("Arial", 9),
            fg='#3498db',
            bg='#2c3e50'
        )
        status_label.pack(pady=5)
        
        # 退出按钮
        quit_button = tk.Button(
            self.root,
            text="退出程序",
            command=self.quit_app,
            font=("Arial", 10),
            bg='#e74c3c',
            fg='white',
            relief='raised',
            bd=2,
            padx=15,
            pady=3
        )
        quit_button.pack(pady=15)
        
    def start_keyboard_monitor(self):
        """启动键盘监听（使用外部进程方式）"""
        # 创建临时文件用于进程间通信
        self.temp_file = tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.txt')
        self.temp_file.close()
        
        # 创建键盘监听脚本
        monitor_script = f'''
import sys
sys.path.insert(0, "{os.path.dirname(os.path.abspath(__file__))}")
from pynput import keyboard
import time

def on_press(key):
    try:
        key_char = key.char.lower() if hasattr(key, 'char') and key.char else None
        if key_char and key_char in "1234567890qwertyuiop":
            with open("{self.temp_file.name}", "w") as f:
                f.write(f"{{key_char}}\\n{{time.time()}}")
    except:
        pass

listener = keyboard.Listener(on_press=on_press)
listener.start()
listener.join()
'''
        
        # 保存监听脚本
        self.monitor_script_file = tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False)
        self.monitor_script_file.write(monitor_script)
        self.monitor_script_file.close()
        
        # 启动监听进程
        def start_monitor():
            try:
                self.monitor_process = subprocess.Popen([
                    sys.executable, self.monitor_script_file.name
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                self.status_var.set("✅ 键盘监听已启动")
                print("✅ 键盘监听进程启动成功")
            except Exception as e:
                self.status_var.set(f"❌ 键盘监听启动失败: {e}")
                print(f"❌ 键盘监听启动失败: {e}")
        
        threading.Thread(target=start_monitor, daemon=True).start()
        
    def check_keys(self):
        """检查按键文件"""
        try:
            if os.path.exists(self.temp_file.name):
                with open(self.temp_file.name, 'r') as f:
                    content = f.read().strip()
                    if content:
                        lines = content.split('\n')
                        if len(lines) >= 2:
                            key_char = lines[0]
                            timestamp = float(lines[1])
                            
                            # 检查是否是新按键
                            key_id = f"{key_char}_{timestamp}"
                            if key_id not in self.last_keys:
                                self.last_keys.add(key_id)
                                if key_char in self.funny_sounds:
                                    self.play_funny_sound(key_char)
                                
                                # 清理旧的按键记录
                                if len(self.last_keys) > 10:
                                    self.last_keys.clear()
        except Exception as e:
            print(f"检查按键文件错误: {e}")
        
        # 继续检查
        if self.running:
            self.root.after(100, self.check_keys)
            
    def play_funny_sound(self, key_char):
        """播放搞笑语音"""
        try:
            sound_text = self.funny_sounds[key_char]
            
            # 更新GUI显示
            self.current_key_var.set(f"按键: {key_char.upper()}")
            self.speech_var.set(f"🔊 {sound_text}")
            self.key_count += 1
            self.stats_var.set(f"按键次数: {self.key_count}")
            
            # 在新线程中播放语音
            def speak():
                try:
                    subprocess.run(["say", sound_text], check=True)
                except Exception as e:
                    print(f"语音播放错误: {e}")
            
            threading.Thread(target=speak, daemon=True).start()
            
            # 添加视觉效果
            self.add_visual_effect()
            
        except Exception as e:
            print(f"播放语音时出错: {e}")
            
    def add_visual_effect(self):
        """添加视觉效果"""
        try:
            colors = ['#e74c3c', '#f39c12', '#27ae60', '#3498db', '#9b59b6']
            original_color = '#2c3e50'
            
            self.root.configure(bg=random.choice(colors))
            self.root.after(100, lambda: self.root.configure(bg=original_color))
        except Exception as e:
            print(f"视觉效果错误: {e}")
        
    def quit_app(self):
        """退出应用"""
        print("🛑 正在退出程序...")
        self.running = False
        
        try:
            if hasattr(self, 'monitor_process'):
                self.monitor_process.terminate()
        except:
            pass
            
        try:
            os.unlink(self.temp_file.name)
            os.unlink(self.monitor_script_file.name)
        except:
            pass
            
        try:
            self.root.quit()
            self.root.destroy()
        except:
            pass
        
    def run(self):
        """运行应用"""
        try:
            print("✅ GUI初始化完成")
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n🛑 用户中断程序")
            self.quit_app()
        except Exception as e:
            print(f"❌ 程序运行错误: {e}")
            self.quit_app()

if __name__ == "__main__":
    print("🎮 启动搞笑按键语音玩具 - 工作版...")
    print("按下数字键 0-9 或字母键 Q-P 来听搞笑语音！")
    print("关闭窗口或按 Ctrl+C 退出程序")
    print("✅ 使用系统语音引擎 (macOS say 命令)")
    
    app = FunnyKeyboardToy()
    app.run()
