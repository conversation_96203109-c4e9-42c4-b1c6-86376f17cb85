# 🎮 搞笑按键语音玩具 - LookMyEyes 解压神器

一个有趣的解压小工具，按下键盘按键时播放搞笑的中文语音，帮助你放松心情！

## ✨ 功能特点

- 🎯 **按键检测**: 监听键盘按键（数字键0-9，字母键Q-P）
- 🔊 **搞笑语音**: 每个按键对应不同的搞笑中文语音
- 🎨 **炫酷界面**: 简洁美观的图形界面，带有视觉效果
- 📊 **统计功能**: 显示按键次数统计
- 🌈 **视觉反馈**: 按键时有颜色闪烁效果

## 🚀 快速开始

### 🏆 推荐方式：直接运行工作版
```bash
# 消除Tk警告并启动GUI版本（推荐）
TK_SILENCE_DEPRECATION=1 ./venv/bin/python main_working.py

# 或启动命令行版本（最稳定）
TK_SILENCE_DEPRECATION=1 ./venv/bin/python main_cli.py
```

### 方法一：使用启动器
```bash
python start.py
```
启动器会自动检测环境并提供两种安装选项：
- **虚拟环境安装**：依赖包安装在项目文件夹的 `venv/` 目录下
- **全局安装**：依赖包安装在系统Python环境中

### 方法二：手动创建虚拟环境
```bash
# 1. 创建虚拟环境
python3 -m venv venv

# 2. 激活虚拟环境
source venv/bin/activate  # macOS/Linux
# 或 venv\Scripts\activate  # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 运行程序（选择一个版本）
TK_SILENCE_DEPRECATION=1 python main_working.py  # GUI版本
# 或
TK_SILENCE_DEPRECATION=1 python main_cli.py      # 命令行版本
```

## 📦 依赖包安装位置说明

### 🏠 虚拟环境安装（推荐）
- **位置**：`./venv/lib/python3.9/site-packages/`
- **优点**：项目独立，不影响其他Python项目
- **缺点**：每个项目都需要单独安装

### 🌍 全局安装
- **位置**：`/Users/<USER>/Library/Python/3.9/lib/python/site-packages/`
- **优点**：所有项目共享，节省空间
- **缺点**：可能产生版本冲突

## 🎮 游戏版本说明

### 🏆 工作版 (main_working.py) - 推荐
- **特点**: 完整GUI界面 + 视觉效果 + 按键统计
- **优势**: 解决了macOS上tkinter和pynput的冲突问题
- **启动**: `TK_SILENCE_DEPRECATION=1 ./venv/bin/python main_working.py`

### 🥈 命令行版 (main_cli.py) - 最稳定
- **特点**: 纯命令行界面，无GUI
- **优势**: 最稳定，资源占用最少
- **启动**: `TK_SILENCE_DEPRECATION=1 ./venv/bin/python main_cli.py`

## 🎮 使用说明

1. 启动程序后（GUI版本会出现图形界面）
2. 按下以下按键来听搞笑语音：
   - **数字键 0-9**: 各种搞笑回应
   - **字母键 Q-P**: 更多有趣语音

### 按键对应语音示例：
- `1` → "回答我！为什么不说话？"
- `2` → "看着我的眼睛！不要逃避！"
- `3` → "告诉我为什么？我很好奇！"
- `Q` → "什么鬼？这是什么操作？"
- `W` → "我的天哪！吓死我了！"
- ...更多搞笑语音等你发现！

## 🛠️ 技术特性

- **跨平台**: 支持 Windows、macOS、Linux
- **全局监听**: 无需程序获得焦点即可响应按键
- **多线程**: 语音播放不会阻塞界面
- **中文语音**: 自动检测并使用中文语音引擎

## 📋 系统要求

- Python 3.6+
- 支持的操作系统：Windows/macOS/Linux
- 音频输出设备

## 🔧 依赖包

- `pynput`: 全局键盘监听
- `pyttsx3`: 文本转语音
- `tkinter`: 图形界面（Python内置）

## 🎯 自定义语音

你可以编辑 `main_working.py` 或 `main_cli.py` 文件中的 `funny_sounds` 字典来自定义按键对应的语音内容：

```python
self.funny_sounds = {
    '1': "你的自定义语音内容",
    '2': "另一个搞笑语音",
    # 添加更多...
}
```

## 🐛 常见问题

### Q: 没有声音？
A: 请检查：
- 系统音量是否开启
- 是否安装了语音引擎
- 尝试重启程序

### Q: 按键没有反应？
A: 请确保：
- 程序正在运行
- 按下的是支持的按键（0-9, Q-P）
- 程序有足够的权限

### Q: 语音不是中文？
A: 程序会自动检测中文语音引擎，如果没有中文语音，会使用系统默认语音。

## 🎉 享受游戏

现在开始按键盘，享受搞笑的语音反馈吧！这是一个很好的解压工具，特别适合：
- 工作间隙放松
- 逗朋友开心
- 测试键盘手感
- 纯粹的娱乐

## 📝 许可证

本项目仅供学习和娱乐使用。

---

**祝你玩得开心！🎮✨**
