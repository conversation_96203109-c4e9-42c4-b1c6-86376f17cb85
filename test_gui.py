#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI版本 - 最简化版本
用于诊断macOS GUI问题
"""

import tkinter as tk
import sys

def test_basic_gui():
    """测试基本GUI功能"""
    print("🔍 测试基本GUI功能...")
    
    try:
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("300x200")
        
        label = tk.Label(root, text="GUI测试成功！", font=("Arial", 14))
        label.pack(pady=50)
        
        button = tk.Button(root, text="关闭", command=root.quit)
        button.pack(pady=20)
        
        print("✅ GUI创建成功")
        root.mainloop()
        print("✅ GUI运行完成")
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")

def test_pynput_only():
    """测试pynput功能"""
    print("🔍 测试pynput功能...")
    
    try:
        from pynput import keyboard
        import time
        
        def on_press(key):
            try:
                if hasattr(key, 'char') and key.char:
                    print(f"按键: {key.char}")
                    if key.char.lower() == 'q':
                        print("退出测试")
                        return False
            except:
                pass
        
        print("✅ 开始监听按键，按 'q' 退出...")
        listener = keyboard.Listener(on_press=on_press)
        listener.start()
        listener.join()
        print("✅ pynput测试完成")
        
    except Exception as e:
        print(f"❌ pynput测试失败: {e}")

if __name__ == "__main__":
    print("🧪 开始诊断测试...")
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "gui":
            test_basic_gui()
        elif sys.argv[1] == "pynput":
            test_pynput_only()
        else:
            print("用法: python test_gui.py [gui|pynput]")
    else:
        print("选择测试类型:")
        print("1. GUI测试: python test_gui.py gui")
        print("2. pynput测试: python test_gui.py pynput")
