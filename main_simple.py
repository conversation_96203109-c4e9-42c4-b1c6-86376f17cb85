#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搞笑按键语音玩具 - 简化版本
使用系统语音，更稳定可靠
"""

import tkinter as tk
import threading
import subprocess
import sys
from pynput import keyboard
import random

class FunnyKeyboardToy:
    def __init__(self):
        # 搞笑语音内容映射
        self.funny_sounds = {
            '1': "回答我！为什么不说话？",
            '2': "看着我的眼睛！不要逃避！",
            '3': "告诉我为什么？我很好奇！",
            '4': "你在干什么？快说实话！",
            '5': "不可能！我不相信！",
            '6': "哈哈哈哈！太搞笑了！",
            '7': "我不信！你在骗我！",
            '8': "真的吗？确定不是开玩笑？",
            '9': "太离谱了！怎么可能！",
            '0': "好吧好吧，我投降了！",
            'q': "什么鬼？这是什么操作？",
            'w': "我的天哪！吓死我了！",
            'e': "额...这个...怎么说呢...",
            'r': "让我想想...嗯...不对！",
            't': "太好玩了！再来一次！",
            'y': "耶！成功了！",
            'u': "呃...你确定吗？",
            'i': "我觉得不对劲...",
            'o': "哦！原来如此！",
            'p': "噗！忍不住笑了！"
        }
        
        # 创建GUI
        self.setup_gui()
        
        # 启动键盘监听
        self.start_keyboard_listener()
        
        print("✅ 使用系统语音引擎 (macOS say 命令)")
        
    def setup_gui(self):
        """创建图形界面"""
        self.root = tk.Tk()
        self.root.title("🎮 搞笑按键语音玩具 - LookMyEyes 解压神器")
        self.root.geometry("600x500")
        self.root.configure(bg='#2c3e50')
        
        # 标题
        title_label = tk.Label(
            self.root, 
            text="🎮 搞笑按键语音玩具", 
            font=("Arial", 20, "bold"),
            fg='#ecf0f1',
            bg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # 副标题
        subtitle_label = tk.Label(
            self.root, 
            text="按下键盘按键听搞笑语音，解压神器！", 
            font=("Arial", 12),
            fg='#bdc3c7',
            bg='#2c3e50'
        )
        subtitle_label.pack(pady=5)
        
        # 当前按键显示
        self.current_key_var = tk.StringVar(value="等待按键...")
        current_key_label = tk.Label(
            self.root,
            textvariable=self.current_key_var,
            font=("Arial", 16, "bold"),
            fg='#e74c3c',
            bg='#34495e',
            relief='raised',
            bd=3,
            padx=20,
            pady=10
        )
        current_key_label.pack(pady=20)
        
        # 语音内容显示
        self.speech_var = tk.StringVar(value="按下按键开始游戏！")
        speech_label = tk.Label(
            self.root,
            textvariable=self.speech_var,
            font=("Arial", 14),
            fg='#f39c12',
            bg='#2c3e50',
            wraplength=500,
            justify='center'
        )
        speech_label.pack(pady=10)
        
        # 按键说明
        instruction_frame = tk.Frame(self.root, bg='#2c3e50')
        instruction_frame.pack(pady=20, padx=20, fill='x')
        
        instruction_label = tk.Label(
            instruction_frame,
            text="🎯 支持的按键：数字键 0-9, 字母键 Q-P",
            font=("Arial", 12),
            fg='#95a5a6',
            bg='#2c3e50'
        )
        instruction_label.pack()
        
        # 统计信息
        self.stats_var = tk.StringVar(value="按键次数: 0")
        stats_label = tk.Label(
            self.root,
            textvariable=self.stats_var,
            font=("Arial", 10),
            fg='#7f8c8d',
            bg='#2c3e50'
        )
        stats_label.pack(pady=10)
        
        # 退出按钮
        quit_button = tk.Button(
            self.root,
            text="退出程序",
            command=self.quit_app,
            font=("Arial", 12),
            bg='#e74c3c',
            fg='white',
            relief='raised',
            bd=2,
            padx=20,
            pady=5
        )
        quit_button.pack(pady=20)
        
        self.key_count = 0
        
    def start_keyboard_listener(self):
        """启动键盘监听器"""
        def on_press(key):
            try:
                # 获取按键字符
                key_char = key.char.lower() if hasattr(key, 'char') and key.char else None

                if key_char and key_char in self.funny_sounds:
                    # 使用线程安全的方式更新GUI
                    self.root.after(0, lambda: self.play_funny_sound(key_char))

            except AttributeError:
                # 特殊按键（如方向键等）
                pass
            except Exception as e:
                print(f"按键处理错误: {e}")

        # 在新线程中启动监听器，添加错误处理
        try:
            self.listener = keyboard.Listener(on_press=on_press)
            self.listener.start()
            print("✅ 键盘监听器启动成功")
        except Exception as e:
            print(f"❌ 键盘监听器启动失败: {e}")
            print("请确保已授予辅助功能权限")
        
    def play_funny_sound(self, key_char):
        """播放搞笑语音"""
        try:
            sound_text = self.funny_sounds[key_char]

            # 更新GUI显示
            self.current_key_var.set(f"按键: {key_char.upper()}")
            self.speech_var.set(f"🔊 {sound_text}")
            self.key_count += 1
            self.stats_var.set(f"按键次数: {self.key_count}")

            # 在新线程中播放语音，避免阻塞GUI
            def speak():
                try:
                    # 使用 macOS 系统语音
                    subprocess.run(["say", sound_text], check=True)
                except subprocess.CalledProcessError:
                    print(f"语音播放失败: {sound_text}")
                except FileNotFoundError:
                    print("系统语音命令不可用，请确保在 macOS 系统上运行")

            threading.Thread(target=speak, daemon=True).start()

            # 添加视觉效果
            self.add_visual_effect()

        except Exception as e:
            print(f"播放语音时出错: {e}")
        
    def add_visual_effect(self):
        """添加视觉效果"""
        # 随机改变背景色一下下
        colors = ['#e74c3c', '#f39c12', '#27ae60', '#3498db', '#9b59b6']
        original_color = '#2c3e50'
        
        def flash():
            self.root.configure(bg=random.choice(colors))
            self.root.after(100, lambda: self.root.configure(bg=original_color))
        
        flash()
        
    def quit_app(self):
        """退出应用"""
        if hasattr(self, 'listener'):
            self.listener.stop()
        self.root.quit()
        self.root.destroy()
        
    def run(self):
        """运行应用"""
        try:
            # 确保GUI在主线程中运行
            self.root.after(100, lambda: print("✅ GUI初始化完成"))
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n🛑 用户中断程序")
            self.quit_app()
        except Exception as e:
            print(f"❌ 程序运行错误: {e}")
            self.quit_app()

if __name__ == "__main__":
    print("🎮 启动搞笑按键语音玩具...")
    print("按下数字键 0-9 或字母键 Q-P 来听搞笑语音！")
    print("关闭窗口或按 Ctrl+C 退出程序")
    
    app = FunnyKeyboardToy()
    app.run()
